<?php

namespace MadHat\SiteIntegrationOrderstatus\Model;

use MadHat\DbLogger\Logger\DbLoggerSaver;
use MadHat\DbLogger\Model\Config\Source\LogIdentifierProvider;
use MadHat\OrderIntegration\Api\MadhatOrderInfoRepositoryInterface;
use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\DatabaseOperations;
use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\OrderItemManager;
use MadHat\SiteIntegrationCore\Model\Api\SiteOrderApi\OrderStatusManager;
use MadHat\SiteIntegrationOrderstatus\Api\Data\OrderstatusDataInterface;
use Magento\Framework\Api\SearchCriteriaBuilder;
use Magento\Framework\Event\ManagerInterface as EventManagerInterface;
use Magento\Framework\Exception\LocalizedException;
use Magento\Framework\Exception\NoSuchEntityException;
use Magento\Sales\Api\Data\OrderInterface;
use Magento\Sales\Api\OrderRepositoryInterface;

class OrderstatusProcessor
{
    protected const PICKED_STATUS = 'Picked';
    protected const INVOICED_STATUS = 'Invoiced';
    protected const CANCELED_STATUS = 'Cancelled';

    /** @var OrderRepositoryInterface */
    private OrderRepositoryInterface $orderRepository;

    /** @var SearchCriteriaBuilder */
    private SearchCriteriaBuilder $searchCriteriaBuilder;

    /** @var OrderStatusManager */
    private OrderStatusManager $orderStatusManager;

    /** @var OrderItemManager */
    private OrderItemManager $orderItemManager;

    /** @var DatabaseOperations */
    private DatabaseOperations $databaseOperations;

    /** @var MadhatOrderInfoRepositoryInterface */
    private MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository;

    /** @var DbLoggerSaver */
    private DbLoggerSaver $dbLoggerSaver;

    /** @var EventManagerInterface */
    private EventManagerInterface $eventManager;

    /**
     * @param OrderRepositoryInterface $orderRepository
     * @param SearchCriteriaBuilder $searchCriteriaBuilder
     * @param OrderStatusManager $orderStatusManager
     * @param OrderItemManager $orderItemManager
     * @param DatabaseOperations $databaseOperations
     * @param MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository
     * @param DbLoggerSaver $dbLoggerSaver
     * @param EventManagerInterface $eventManager
     */
    public function __construct(
        OrderRepositoryInterface $orderRepository,
        SearchCriteriaBuilder $searchCriteriaBuilder,
        OrderStatusManager $orderStatusManager,
        OrderItemManager $orderItemManager,
        DatabaseOperations $databaseOperations,
        MadhatOrderInfoRepositoryInterface $madhatOrderInfoRepository,
        DbLoggerSaver $dbLoggerSaver,
        EventManagerInterface $eventManager
    ) {
        $this->orderRepository = $orderRepository;
        $this->searchCriteriaBuilder = $searchCriteriaBuilder;
        $this->orderStatusManager = $orderStatusManager;
        $this->orderItemManager = $orderItemManager;
        $this->databaseOperations = $databaseOperations;
        $this->madhatOrderInfoRepository = $madhatOrderInfoRepository;
        $this->dbLoggerSaver = $dbLoggerSaver;
        $this->eventManager = $eventManager;
    }

    /**
     * @param OrderstatusDataInterface $item
     * @return bool
     */
    public function processItem(OrderstatusDataInterface $item): bool
    {
        $webOrderNo = $item->getWebOrderNo();
        if (!$webOrderNo) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                'WebOrderNo is missing in the message.',
                'warning',
                LogIdentifierProvider::ORDER
            );
            return false;
        }

        try {
            // WebOrderNo in RabbitMQ is actually the entity_id, not increment_id
            $order = $this->loadOrderByEntityId((int)$webOrderNo);
            if ($order) {
                $this->dbLoggerSaver->addRecord(
                    'Order Status Update Processing',
                    sprintf(
                        'Processing order status update for WebOrderNo %s (entity_id). Status: %s (%s)',
                        $webOrderNo,
                        $item->getStatusText(),
                        $item->getStatus()
                    ),
                    'info',
                    LogIdentifierProvider::ORDER
                );

                // Convert RabbitMQ data to API response format and process
                $response = $this->convertToApiResponseFormat($item);
                return $this->handleOrderStatusUpdate($order, $response);
            } else {
                $this->dbLoggerSaver->addRecord(
                    'Order Status Update Error',
                    sprintf('Order with WebOrderNo %s (entity_id) not found.', $webOrderNo),
                    'warning',
                    LogIdentifierProvider::ORDER
                );
                return false;
            }
        } catch (NoSuchEntityException $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Order with WebOrderNo %s not found: %s', $webOrderNo, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        } catch (LocalizedException $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Error processing order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Unexpected error processing order with WebOrderNo %s: %s', $webOrderNo, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        }
    }

    /**
     * Convert RabbitMQ data format to API response format
     *
     * @param OrderstatusDataInterface $item
     * @return array
     */
    private function convertToApiResponseFormat(OrderstatusDataInterface $item): array
    {
        $response = [
            'ResponseResult' => ['Code' => 'SUCCESS'],
            'OrderStatusData' => [
                'Status' => $this->mapStatusTextToExpectedFormat($item->getStatusText()),
                'OrderRowsData' => [],
                'Billings' => []
            ]
        ];

        // Convert OrderRowsData if available
        if ($item->getOrderRowsData()) {
            foreach ($item->getOrderRowsData() as $orderRow) {
                $response['OrderStatusData']['OrderRowsData'][] = [
                    'ProductNo' => $orderRow->getProductNo() ?? '',
                    'PriceGross' => $orderRow->getPriceGross() ?? 0.0,
                    'Quantity' => $orderRow->getQuantity() ?? 0,
                    'PriceNet' => $orderRow->getPriceNet() ?? 0.0
                ];
            }
        }

        // Convert Billings if available
        if ($item->getBillings()) {
            foreach ($item->getBillings() as $billing) {
                $response['OrderStatusData']['Billings'][] = [
                    'InvoiceNo' => $billing->getInvoiceNo() ?? '',
                    'InvoiceFile' => $billing->getInvoiceFile() ?? '',
                    'InvoiceUrl' => $billing->getInvoiceUrl() ?? ''
                ];
            }
        }

        // Add FulfilmentWorkflow if available
        if ($item->getFulfilmentWorkflow()) {
            $response['OrderStatusData']['FulfilmentWorkflow'] = $item->getFulfilmentWorkflow();
        }

        return $response;
    }

    /**
     * Map RabbitMQ status text to expected format
     *
     * @param string|null $statusText
     * @return string
     */
    private function mapStatusTextToExpectedFormat(?string $statusText): string
    {
        if (!$statusText) {
            return '';
        }

        // Map uppercase RabbitMQ status to title case expected by the system
        $statusMapping = [
            'PICKED' => self::PICKED_STATUS,
            'INVOICED' => self::INVOICED_STATUS,
            'CANCELLED' => self::CANCELED_STATUS,
            // Also handle title case in case it comes in that format
            'Picked' => self::PICKED_STATUS,
            'Invoiced' => self::INVOICED_STATUS,
            'Cancelled' => self::CANCELED_STATUS,
        ];

        return $statusMapping[$statusText] ?? $statusText;
    }

    /**
     * Handle order status update based on status
     *
     * @param OrderInterface $order
     * @param array $response
     * @return bool
     */
    private function handleOrderStatusUpdate(OrderInterface $order, array $response): bool
    {
        try {
            $magentoOrderId = (int)$order->getEntityId();
            $madhatOrderInfo = $this->madhatOrderInfoRepository->getByOrderId($magentoOrderId);
            $status = $response['OrderStatusData']['Status'];

            switch ($status) {
                case self::PICKED_STATUS:
                    return $this->handlePickedStatus($magentoOrderId, $order, $response, $madhatOrderInfo);

                case self::INVOICED_STATUS:
                    return $this->handleInvoicedStatus($magentoOrderId, $response, $madhatOrderInfo);

                case self::CANCELED_STATUS:
                    return $this->handleCanceledStatus($order);

                default:
                    $this->dbLoggerSaver->addRecord(
                        'Order Status Update Warning',
                        sprintf('Unknown status "%s" for order %s', $status, $order->getIncrementId()),
                        'warning',
                        LogIdentifierProvider::ORDER
                    );
                    return false;
            }
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Error handling status update for order %s: %s', $order->getIncrementId(), $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        }
    }

    /**
     * Load order by entity ID
     *
     * @param int $entityId
     * @return OrderInterface|null
     */
    private function loadOrderByEntityId(int $entityId): ?OrderInterface
    {
        try {
            return $this->orderRepository->get($entityId);
        } catch (NoSuchEntityException $e) {
            return null;
        }
    }

    /**
     * Load order by increment ID
     *
     * @param string $incrementId
     * @return OrderInterface|null
     */
    private function loadOrderByIncrementId(string $incrementId): ?OrderInterface
    {
        $searchCriteria = $this->searchCriteriaBuilder
            ->addFilter('increment_id', $incrementId)
            ->create();

        $orders = $this->orderRepository->getList($searchCriteria)->getItems();

        return !empty($orders) ? reset($orders) : null;
    }

    /**
     * Handle Picked status
     *
     * @param int $magentoOrderId
     * @param OrderInterface $order
     * @param array $response
     * @param mixed $madhatOrderInfo
     * @return bool
     */
    private function handlePickedStatus(int $magentoOrderId, OrderInterface $order, array $response, $madhatOrderInfo): bool
    {
        try {
            if (!$madhatOrderInfo->getSiteIsPicked()) {
                // Edit order items and totals if OrderRowsData is present
                if (!empty($response['OrderStatusData']['OrderRowsData'])) {
                    $this->orderItemManager->editOrderItems($magentoOrderId, $response['OrderStatusData']);
                    $this->orderItemManager->editOrderTotals($magentoOrderId, $response['OrderStatusData']);
                }

                // Save picked status
                $this->orderStatusManager->savePickedStatus($magentoOrderId, $response);

                // Update Magento order status to 'processing' when picked
                if ($order->getStatus() === 'pending') {
                    $this->updateMagentoOrderStatus($order, 'processing', 'Order picked via SITE integration');
                }

                // Dispatch event
                $this->eventManager->dispatch('madhat_order_sync_to_erp', [
                    'order_id' => $magentoOrderId,
                    'response' => $response
                ]);

                // Handle FulfilmentWorkflow if present
                if (isset($response['OrderStatusData']['FulfilmentWorkflow'])) {
                    $this->handleFulfilmentWorkflow($response, $madhatOrderInfo);
                }

                $this->dbLoggerSaver->addRecord(
                    'Order Status Update Success',
                    sprintf('Order %s successfully marked as Picked', $order->getIncrementId()),
                    'info',
                    LogIdentifierProvider::ORDER
                );
            } else {
                // Even if already picked, update Magento status if it's still pending
                if ($order->getStatus() === 'pending') {
                    $this->updateMagentoOrderStatus($order, 'processing', 'Order status updated via SITE integration (already picked)');
                }

                $this->dbLoggerSaver->addRecord(
                    'Order Status Update Info',
                    sprintf('Order %s already marked as Picked, skipping', $order->getIncrementId()),
                    'info',
                    LogIdentifierProvider::ORDER
                );
            }

            return true;
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Error handling Picked status for order %s: %s', $order->getIncrementId(), $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        }
    }

    /**
     * Handle Invoiced status
     *
     * @param int $magentoOrderId
     * @param array $response
     * @param mixed $madhatOrderInfo
     * @return bool
     */
    private function handleInvoicedStatus(int $magentoOrderId, array $response, $madhatOrderInfo): bool
    {
        try {
            $order = $this->orderRepository->get($magentoOrderId);

            if (!$madhatOrderInfo->getSiteIsInvoiced() && !empty($response['OrderStatusData']['Billings'])) {
                // Ensure order items reflect latest VISMA state (e.g., removed/modified rows)
                if (!empty($response['OrderStatusData']['OrderRowsData'])) {
                    $this->orderItemManager->editOrderItems($magentoOrderId, $response['OrderStatusData']);
                }

                // Edit order totals after items are synchronized
                $this->orderItemManager->editOrderTotals($magentoOrderId, $response['OrderStatusData']);

                // Save invoiced status
                $this->orderStatusManager->saveInvoicedStatus($magentoOrderId, $response);

                // Update Magento order status to 'processing' when invoiced
                $this->updateMagentoOrderStatus($order, 'processing', 'Order invoiced via SITE integration');

                // Dispatch events
                $this->eventManager->dispatch('madhat_order_sync_invoiced', [
                    'order_id' => $magentoOrderId,
                    'response' => $response
                ]);
                $this->eventManager->dispatch('madhat_order_sync_invoiced_billing', [
                    'order_id' => $magentoOrderId,
                    'response' => $response
                ]);

                $this->dbLoggerSaver->addRecord(
                    'Order Status Update Success',
                    sprintf('Order %s successfully marked as Invoiced and status updated to processing', $order->getIncrementId()),
                    'info',
                    LogIdentifierProvider::ORDER
                );
            } else {
                // Even if already invoiced, update Magento status if it's still pending
                if ($order->getStatus() === 'pending') {
                    $this->updateMagentoOrderStatus($order, 'processing', 'Order status updated via SITE integration (already invoiced)');

                    $this->dbLoggerSaver->addRecord(
                        'Order Status Update Info',
                        sprintf('Order %s already invoiced but Magento status updated to processing', $order->getIncrementId()),
                        'info',
                        LogIdentifierProvider::ORDER
                    );
                } else {
                    $this->dbLoggerSaver->addRecord(
                        'Order Status Update Info',
                        sprintf('Order %s already invoiced and status is %s, skipping', $order->getIncrementId(), $order->getStatus()),
                        'info',
                        LogIdentifierProvider::ORDER
                    );
                }
            }

            return true;
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Error handling Invoiced status for order ID %s: %s', $magentoOrderId, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        }
    }

    /**
     * Handle Canceled status
     *
     * @param OrderInterface $order
     * @return bool
     */
    private function handleCanceledStatus(OrderInterface $order): bool
    {
        try {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Info',
                sprintf('Canceling order %s[#%s]', $order->getId(), $order->getIncrementId()),
                'info',
                LogIdentifierProvider::ORDER
            );

            $this->databaseOperations->directCancelOrder($order);

            $this->dbLoggerSaver->addRecord(
                'Order Status Update Success',
                sprintf('Order %s successfully canceled', $order->getIncrementId()),
                'info',
                LogIdentifierProvider::ORDER
            );

            return true;
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Error canceling order %s: %s', $order->getIncrementId(), $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
            return false;
        }
    }

    /**
     * Handle fulfilment workflow
     *
     * @param array $response
     * @param mixed $madhatOrderInfo
     * @return void
     */
    private function handleFulfilmentWorkflow(array $response, $madhatOrderInfo): void
    {
        try {
            if ($response['OrderStatusData']['FulfilmentWorkflow'] === 'Default') {
                // Note: This would require ScopeConfigInterface to get the default value
                // For now, we'll just set it as 'Default'
                $madhatOrderInfo->setFulfilmentWorkflow('Default');
            } else {
                $madhatOrderInfo->setFulfilmentWorkflow($response['OrderStatusData']['FulfilmentWorkflow']);
            }
            $this->madhatOrderInfoRepository->save($madhatOrderInfo);
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Warning',
                sprintf('Error setting FulfilmentWorkflow: %s', $e->getMessage()),
                'warning',
                LogIdentifierProvider::ORDER
            );
        }
    }

    /**
     * Update Magento order status
     *
     * @param OrderInterface $order
     * @param string $status
     * @param string $comment
     * @return void
     */
    private function updateMagentoOrderStatus(OrderInterface $order, string $status, string $comment): void
    {
        try {
            $order->setStatus($status);
            $order->setState(\Magento\Sales\Model\Order::STATE_PROCESSING);
            $order->addCommentToStatusHistory($comment, $status, false);
            $this->orderRepository->save($order);

            $this->dbLoggerSaver->addRecord(
                'Order Status Update Success',
                sprintf('Magento order %s status updated to %s', $order->getIncrementId(), $status),
                'info',
                LogIdentifierProvider::ORDER
            );
        } catch (\Exception $e) {
            $this->dbLoggerSaver->addRecord(
                'Order Status Update Error',
                sprintf('Error updating Magento order %s status to %s: %s', $order->getIncrementId(), $status, $e->getMessage()),
                'error',
                LogIdentifierProvider::ORDER
            );
        }
    }
}
